"""
對話會話管理服務
處理AI對話的20次限制機制和輪次重置
使用Redis存儲會話狀態
"""

import json
import asyncio
from typing import Optional, Tuple, Dict, Any
from datetime import datetime, timedelta
from app.models.user import User
from app.core.redis_client import get_redis_client
from app.core.logging import get_logger
from app.services.wallet.service import wallet_service
from app.services.system_config_service import system_config_service

logger = get_logger(__name__)


class ChatSessionService:
    """
    聊天会话服务
    
    管理用户的AI对话会话状态，包括：
    - 消息计数
    - 轮次管理
    - 计费状态
    - 对话会话追踪
    """
    
    def __init__(self):
        self._redis_client = get_redis_client()
    
    async def _get_session_ttl(self) -> int:
        """获取会话数据TTL（Redis存储过期时间）"""
        value = await system_config_service.get_config("chat_session_ttl")
        return int(value)
    
    async def _get_session_timeout(self) -> int:
        """获取会话超时时间（业务逻辑超时）"""
        value = await system_config_service.get_config("chat_session_timeout")
        return int(value)
    
    async def _get_max_messages_per_round(self) -> int:
        """获取每轮最大消息数"""
        value = await system_config_service.get_config("chat_max_messages_per_round")
        return int(value)
    
    async def get_user_session_info(self, user_id: str, conversation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取用户会话信息
        
        Args:
            user_id: 用户ID
            conversation_id: 对话会话ID（可选）
            
        Returns:
            会话信息字典
        """
        session_key = f"chat_session:{user_id}"
        
        # 获取会话数据
        session_data = await self._redis_client.get(session_key)
        
        if not session_data:
            # 新会话
            session_info = {
                "current_round": 1,
                "message_count": 0,
                "last_reset_time": datetime.now().isoformat(),
                "needs_new_charge": True,
                "current_conversation_id": conversation_id,
                "session_start_time": datetime.now().isoformat(),
                "last_activity_time": datetime.now().isoformat()
            }
            
            # 保存到Redis
            session_ttl = await self._get_session_ttl()
            await self._redis_client.setex(session_key, session_ttl, json.dumps(session_info))
            logger.info(f"为用户 {user_id} 创建新会话")
            return session_info
        
        # 解析现有会话数据
        session_info = json.loads(session_data)
        
        # 检查会话是否超时
        session_timeout = await self._get_session_timeout()
        last_activity = datetime.fromisoformat(session_info.get("last_activity_time", session_info.get("session_start_time", datetime.now().isoformat())))
        if datetime.now() - last_activity > timedelta(seconds=session_timeout):
            # 会话超时，重置为需要重新计费
            session_info["needs_new_charge"] = True
            session_info["message_count"] = 0
            session_info["current_round"] = session_info.get("current_round", 1) + 1
            session_info["last_reset_time"] = datetime.now().isoformat()
            session_info["session_start_time"] = datetime.now().isoformat()
            session_info["current_conversation_id"] = conversation_id
            logger.info(f"用户 {user_id} 会话超时，重置为需要重新计费")
        
        # 检查是否为新的对话会话
        if conversation_id and conversation_id != session_info.get("current_conversation_id"):
            # 新对话会话，需要重新计费
            session_info["current_conversation_id"] = conversation_id
            session_info["needs_new_charge"] = True
            session_info["message_count"] = 0  # 重置消息计数
            session_info["current_round"] = session_info.get("current_round", 1) + 1
            session_info["last_reset_time"] = datetime.now().isoformat()
            session_info["session_start_time"] = datetime.now().isoformat()
            
            # 更新Redis中的会话信息
            session_ttl = await self._get_session_ttl()
            await self._redis_client.setex(session_key, session_ttl, json.dumps(session_info))
            logger.info(f"用户 {user_id} 开启新对话会话 {conversation_id}，需要重新计费")
        elif conversation_id and conversation_id == session_info.get("current_conversation_id"):
            # 同一个对话会话，不需要重新计费
            session_info["needs_new_charge"] = False
        
        # 更新最后活动时间
        session_info["last_activity_time"] = datetime.now().isoformat()
        session_ttl = await self._get_session_ttl()
        await self._redis_client.setex(session_key, session_ttl, json.dumps(session_info))
        
        return session_info

    async def increment_message_count(self, user_id: str, conversation_id: Optional[str] = None) -> Tuple[int, bool]:
        """
        增加消息计数并检查是否需要重新计费
        
        Args:
            user_id: 用户ID
            conversation_id: 对话会话ID（可选）
            
        Returns:
            (消息计数, 是否需要重新计费)
        """
        session_key = f"chat_session:{user_id}"
        
        # 获取配置值
        session_ttl = await self._get_session_ttl()
        max_messages = await self._get_max_messages_per_round()
        
        # 使用Lua脚本确保原子性操作
        lua_script = """
        local session_key = KEYS[1]
        local session_ttl = tonumber(ARGV[1])
        local max_messages = tonumber(ARGV[2])
        local current_time = ARGV[3]
        local conversation_id = ARGV[4]

        -- 获取当前会话数据
        local session_data = redis.call('GET', session_key)
        local session_info

        if not session_data then
            -- 如果没有会话数据，说明get_user_session_info没有被调用，这是异常情况
            -- 返回错误，要求先调用get_user_session_info
            return cjson.encode({
                error = "Session not initialized. Call get_user_session_info first."
            })
        else
            -- 解析现有会话数据
            session_info = cjson.decode(session_data)
            local current_count = session_info.message_count or 0
            local new_count = current_count + 1
            
            -- 检查是否为新的对话会话
            if conversation_id and conversation_id ~= (session_info.current_conversation_id or "") then
                -- 新对话会话，需要重新计费
                session_info.current_conversation_id = conversation_id
                session_info.needs_new_charge = true
                session_info.message_count = 1
                session_info.current_round = (session_info.current_round or 1) + 1
                session_info.last_reset_time = current_time
                session_info.session_start_time = current_time
                new_count = 1
            else
                -- 检查是否需要重新计费（基于消息数量）
                if new_count > max_messages then
                    -- 重置为新的一轮
                    session_info.current_round = (session_info.current_round or 1) + 1
                    session_info.message_count = 1
                    session_info.last_reset_time = current_time
                    session_info.needs_new_charge = true
                    new_count = 1
                else
                    session_info.message_count = new_count
                    session_info.needs_new_charge = false
                end
            end
            
            -- 确保message_count被正确设置
            session_info.message_count = new_count
            session_info.last_activity_time = current_time
        end
        
        -- 保存会话数据
        redis.call('SETEX', session_key, session_ttl, cjson.encode(session_info))
        
        -- 返回结果
        return cjson.encode({
            message_count = session_info.message_count,
            needs_new_charge = session_info.needs_new_charge
        })
        """
        
        try:
            result = await self._redis_client.eval(
                lua_script,
                1,  # 1个key
                session_key,
                session_ttl,
                max_messages,
                datetime.now().isoformat(),
                conversation_id or ""
            )

            if result:
                result_data = json.loads(result)
                # 检查是否有错误
                if "error" in result_data:
                    logger.error(f"会话未初始化，用户: {user_id}, 错误: {result_data['error']}")
                    # 如果会话未初始化，先调用get_user_session_info初始化会话
                    await self.get_user_session_info(user_id, conversation_id)
                    # 重新执行Lua脚本
                    result = await self._redis_client.eval(
                        lua_script,
                        1,
                        session_key,
                        session_ttl,
                        max_messages,
                        datetime.now().isoformat(),
                        conversation_id or ""
                    )
                    if result:
                        result_data = json.loads(result)
                        if "error" not in result_data:
                            return result_data["message_count"], result_data["needs_new_charge"]

                    logger.error(f"重新初始化后仍然失败，用户: {user_id}")
                    return 1, True
                else:
                    return result_data["message_count"], result_data["needs_new_charge"]
            else:
                logger.error(f"Lua脚本执行失败，用户: {user_id}")
                return 1, True

        except Exception as e:
            logger.error(f"增加消息计数失败，用户: {user_id}, 错误: {e}")
            return 1, True

    async def get_remaining_messages(self, user_id: str) -> int:
        """
        获取用户当前轮次剩余消息数
        
        Args:
            user_id: 用户ID
            
        Returns:
            剩余消息数
        """
        session_info = await self.get_user_session_info(user_id)
        current_count = session_info.get("message_count", 0)
        max_messages = await self._get_max_messages_per_round()
        return max(0, max_messages - current_count)

    async def reset_session(self, user_id: str) -> None:
        """
        重置用户会话
        
        Args:
            user_id: 用户ID
        """
        session_key = f"chat_session:{user_id}"
        await self._redis_client.delete(session_key)
        logger.info(f"用户 {user_id} 会话已重置")

    async def handle_session_timeout(self, user_id: str, order_id: Optional[str] = None) -> None:
        """
        处理会话超时，取消未完成的预扣费
        
        Args:
            user_id: 用户ID
            order_id: 预扣费订单ID（可选）
        """
        try:
            if order_id:
                # 取消预扣费
                await wallet_service.charge_cancel(user_id, order_id, None)
                logger.info(f"用户 {user_id} 会话超时，已取消预扣费订单: {order_id}")
            
            # 重置会话状态
            await self.reset_session(user_id)
            
        except Exception as e:
            logger.error(f"处理用户 {user_id} 会话超时失败: {e}")

# 全局服务实例
chat_session_service = ChatSessionService() 